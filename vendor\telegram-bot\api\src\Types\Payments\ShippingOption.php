<?php

namespace TelegramBot\Api\Types\Payments;

use TelegramBot\Api\BaseType;

/**
 * Class ShippingOption
 * This object represents one shipping option.
 *
 * @package TelegramBot\Api\Types\Payments
 */
class ShippingOption extends BaseType
{
    /**
     * @var array
     */
    protected static $requiredParams = ['id', 'title', 'prices'];

    /**
     * @var array
     */
    protected static $map = [
        'id' => true,
        'title' => true,
        'prices' => ArrayOfLabeledPrice::class
    ];

    /**
     * Shipping option identifier
     *
     * @var string
     */
    protected $id;

    /**
     * Option title
     *
     * @var string
     */
    protected $title;

    /**
     * List of price portions
     *
     * @var array
     */
    protected $prices;

    /**
     * <AUTHOR>
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * <AUTHOR>
     *
     * @param string $id
     *
     * @return void
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * <AUTHOR>
     *
     * @param string $title
     *
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * <AUTHOR>
     * @return array
     */
    public function getPrices()
    {
        return $this->prices;
    }

    /**
     * <AUTHOR>
     *
     * @param array $prices
     *
     * @return void
     */
    public function setPrices($prices)
    {
        $this->prices = $prices;
    }
}
