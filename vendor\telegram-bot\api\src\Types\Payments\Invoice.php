<?php

namespace TelegramBot\Api\Types\Payments;

use TelegramBot\Api\BaseType;

/**
 * Class Invoice
 * This object contains basic information about an invoice.
 *
 * @package TelegramBot\Api\Types\Payments
 */
class Invoice extends BaseType
{
    /**
     * @var array
     */
    protected static $requiredParams = ['title', 'description', 'start_parameter', 'currency', 'total_amount'];

    /**
     * @var array
     */
    protected static $map = [
        'title' => true,
        'description' => true,
        'start_parameter' => true,
        'currency' => true,
        'total_amount' => true,
    ];

    /**
     * Product name
     *
     * @var string
     */
    protected $title;

    /**
     * Product description
     *
     * @var string
     */
    protected $description;

    /**
     * Unique bot deep-linking parameter that can be used to generate this invoice
     *
     * @var string
     */
    protected $startParameter;

    /**
     * Three-letter ISO 4217 currency code
     *
     * @var string
     */
    protected $currency;

    /**
     * Total price in the smallest units of the currency
     *
     * @var integer
     */
    protected $totalAmount;

    /**
     * <AUTHOR>
     * @return string
     */
    public function getTitle()
    {
        return $this->title;
    }

    /**
     * <AUTHOR>
     *
     * @param string $title
     *
     * @return void
     */
    public function setTitle($title)
    {
        $this->title = $title;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * <AUTHOR>
     *
     * @param string $description
     *
     * @return void
     */
    public function setDescription($description)
    {
        $this->description = $description;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getStartParameter()
    {
        return $this->startParameter;
    }

    /**
     * <AUTHOR>
     *
     * @param string $startParameter
     *
     * @return void
     */
    public function setStartParameter($startParameter)
    {
        $this->startParameter = $startParameter;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * <AUTHOR>
     *
     * @param string $currency
     *
     * @return void
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * <AUTHOR>
     * @return int
     */
    public function getTotalAmount()
    {
        return $this->totalAmount;
    }

    /**
     * <AUTHOR>
     *
     * @param int $totalAmount
     *
     * @return void
     */
    public function setTotalAmount($totalAmount)
    {
        $this->totalAmount = $totalAmount;
    }
}
