<?php

namespace TelegramBot\Api\Types\Payments;

use TelegramBot\Api\BaseType;

/**
 * Class ShippingAddress
 * This object represents a shipping address.
 *
 * @package TelegramBot\Api\Types\Payments
 */
class ShippingAddress extends BaseType
{
    /**
     * @var array
     */
    protected static $requiredParams = ['country_code', 'state', 'city', 'street_line1', 'street_line2', 'post_code'];

    /**
     * @var array
     */
    protected static $map = [
        'country_code' => true,
        'state' => true,
        'city' => true,
        'street_line1' => true,
        'street_line2' => true,
        'post_code' => true,
    ];

    /**
     * ISO 3166-1 alpha-2 country code
     *
     * @var string
     */
    protected $countryCode;

    /**
     * State, if applicable
     *
     * @var string
     */
    protected $state;

    /**
     * City
     *
     * @var string
     */
    protected $city;

    /**
     * First line for the address
     *
     * @var string
     */
    protected $streetLine1;

    /**
     * Second line for the address
     *
     * @var string
     */
    protected $streetLine2;

    /**
     * Address post code
     *
     * @var string
     */
    protected $postCode;

    /**
     * <AUTHOR>
     * @return string
     */
    public function getCountryCode()
    {
        return $this->countryCode;
    }

    /**
     * <AUTHOR>
     *
     * @param string $countryCode
     *
     * @return void
     */
    public function setCountryCode($countryCode)
    {
        $this->countryCode = $countryCode;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * <AUTHOR>
     *
     * @param string $state
     *
     * @return void
     */
    public function setState($state)
    {
        $this->state = $state;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * <AUTHOR>
     *
     * @param string $city
     *
     * @return void
     */
    public function setCity($city)
    {
        $this->city = $city;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getStreetLine1()
    {
        return $this->streetLine1;
    }

    /**
     * <AUTHOR>
     *
     * @param string $streetLine1
     *
     * @return void
     */
    public function setStreetLine1($streetLine1)
    {
        $this->streetLine1 = $streetLine1;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getStreetLine2()
    {
        return $this->streetLine2;
    }

    /**
     * <AUTHOR>
     *
     * @param string $streetLine2
     *
     * @return void
     */
    public function setStreetLine2($streetLine2)
    {
        $this->streetLine2 = $streetLine2;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getPostCode()
    {
        return $this->postCode;
    }

    /**
     * <AUTHOR>
     *
     * @param string $postCode
     *
     * @return void
     */
    public function setPostCode($postCode)
    {
        $this->postCode = $postCode;
    }
}
