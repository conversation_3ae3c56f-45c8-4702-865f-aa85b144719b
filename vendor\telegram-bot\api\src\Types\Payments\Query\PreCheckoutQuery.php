<?php

namespace TelegramBot\Api\Types\Payments\Query;

use TelegramBot\Api\BaseType;
use TelegramBot\Api\Types\Payments\OrderInfo;
use TelegramBot\Api\Types\User;

/**
 * Class PreCheckoutQuery
 * This object contains information about an incoming pre-checkout query.
 *
 * @package TelegramBot\Api\Types\Payments\Query
 */
class PreCheckoutQuery extends BaseType
{
    /**
     * @var array
     */
    protected static $requiredParams = ['id', 'from', 'currency', 'total_amount', 'invoice_payload'];

    /**
     * @var array
     */
    protected static $map = [
        'id' => true,
        'from' => User::class,
        'currency' => true,
        'total_amount' => true,
        'invoice_payload' => true,
        'shipping_option_id' => true,
        'order_info' => OrderInfo::class
    ];

    /**
     * Unique query identifier
     *
     * @var string
     */
    protected $id;

    /**
     * User who sent the query
     *
     * @var User
     */
    protected $from;

    /**
     * Three-letter ISO 4217 currency code
     *
     * @var string
     */
    protected $currency;

    /**
     * Total price in the smallest units of the currency
     *
     * @var integer
     */
    protected $totalAmount;

    /**
     * Bot specified invoice payload
     *
     * @var string
     */
    protected $invoicePayload;

    /**
     * Optional. Identifier of the shipping option chosen by the user
     *
     * @var string|null
     */
    protected $shippingOptionId;

    /**
     * Optional. Order info provided by the user
     *
     * @var OrderInfo|null
     */
    protected $orderInfo;

    /**
     * <AUTHOR>
     * @return string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * <AUTHOR>
     *
     * @param string $id
     *
     * @return void
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * <AUTHOR>
     * @return User
     */
    public function getFrom()
    {
        return $this->from;
    }

    /**
     * <AUTHOR>
     *
     * @param User $from
     *
     * @return void
     */
    public function setFrom($from)
    {
        $this->from = $from;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * <AUTHOR>
     *
     * @param string $currency
     *
     * @return void
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * <AUTHOR>
     * @return int
     */
    public function getTotalAmount()
    {
        return $this->totalAmount;
    }

    /**
     * <AUTHOR>
     *
     * @param int $totalAmount
     *
     * @return void
     */
    public function setTotalAmount($totalAmount)
    {
        $this->totalAmount = $totalAmount;
    }

    /**
     * <AUTHOR>
     * @return mixed
     */
    public function getInvoicePayload()
    {
        return $this->invoicePayload;
    }

    /**
     * <AUTHOR>
     *
     * @param mixed $invoicePayload
     *
     * @return void
     */
    public function setInvoicePayload($invoicePayload)
    {
        $this->invoicePayload = $invoicePayload;
    }

    /**
     * <AUTHOR>
     *
     * @return null|string
     */
    public function getShippingOptionId()
    {
        return $this->shippingOptionId;
    }

    /**
     * <AUTHOR>
     *
     * @param string $shippingOptionId
     *
     * @return void
     */
    public function setShippingOptionId($shippingOptionId)
    {
        $this->shippingOptionId = $shippingOptionId;
    }

    /**
     * <AUTHOR>
     *
     * @return OrderInfo|null
     */
    public function getOrderInfo()
    {
        return $this->orderInfo;
    }

    /**
     * <AUTHOR>
     *
     * @param OrderInfo $orderInfo
     *
     * @return void
     */
    public function setOrderInfo($orderInfo)
    {
        $this->orderInfo = $orderInfo;
    }
}
