<?php

namespace TelegramBot\Api\Types\Payments;

use TelegramBot\Api\BaseType;

/**
 * Class SuccessfulPayment
 * This object contains basic information about a successful payment.
 *
 * @package TelegramBot\Api\Types\Payments
 */
class SuccessfulPayment extends BaseType
{
    /**
     * @var array
     */
    protected static $requiredParams = [
        'currency',
        'total_amount',
        'invoice_payload',
        'telegram_payment_charge_id',
        'provider_payment_charge_id'
    ];

    /**
     * @var array
     */
    protected static $map = [
        'currency' => true,
        'total_amount' => true,
        'invoice_payload' => true,
        'shipping_option_id' => true,
        'order_info' => OrderInfo::class,
        'telegram_payment_charge_id' => true,
        'provider_payment_charge_id' => true
    ];

    /**
     * Three-letter ISO 4217 currency code
     *
     * @var string
     */
    protected $currency;

    /**
     * Total price in the smallest units of the currency
     *
     * @var integer
     */
    protected $totalAmount;

    /**
     * Bot specified invoice payload
     *
     * @var array
     */
    protected $invoicePayload;

    /**
     * Optional. Identifier of the shipping option chosen by the user
     *
     * @var string|null
     */
    protected $shippingOptionId;

    /**
     * Optional. Order info provided by the user
     *
     * @var OrderInfo|null
     */
    protected $orderInfo;

    /**
     * Telegram payment identifier
     *
     * @var string
     */
    protected $telegramPaymentChargeId;

    /**
     * Provider payment identifier
     *
     * @var string
     */
    protected $providerPaymentChargeId;

    /**
     * <AUTHOR>
     * @return string
     */
    public function getCurrency()
    {
        return $this->currency;
    }

    /**
     * <AUTHOR>
     *
     * @param string $currency
     *
     * @return void
     */
    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    /**
     * <AUTHOR>
     * @return int
     */
    public function getTotalAmount()
    {
        return $this->totalAmount;
    }

    /**
     * <AUTHOR>
     *
     * @param int $totalAmount
     *
     * @return void
     */
    public function setTotalAmount($totalAmount)
    {
        $this->totalAmount = $totalAmount;
    }

    /**
     * <AUTHOR>
     * @return array
     */
    public function getInvoicePayload()
    {
        return $this->invoicePayload;
    }

    /**
     * <AUTHOR>
     *
     * @param array $invoicePayload
     *
     * @return void
     */
    public function setInvoicePayload($invoicePayload)
    {
        $this->invoicePayload = $invoicePayload;
    }

    /**
     * <AUTHOR>
     *
     * @return null|string
     */
    public function getShippingOptionId()
    {
        return $this->shippingOptionId;
    }

    /**
     * <AUTHOR>
     *
     * @param string $shippingOptionId
     *
     * @return void
     */
    public function setShippingOptionId($shippingOptionId)
    {
        $this->shippingOptionId = $shippingOptionId;
    }

    /**
     * <AUTHOR>
     * @return string
     */
    public function getTelegramPaymentChargeId()
    {
        return $this->telegramPaymentChargeId;
    }

    /**
     * <AUTHOR>
     *
     * @param string $telegramPaymentChargeId
     *
     * @return void
     */
    public function setTelegramPaymentChargeId($telegramPaymentChargeId)
    {
        $this->telegramPaymentChargeId = $telegramPaymentChargeId;
    }

    /**
     * <AUTHOR>
     * @return mixed
     */
    public function getProviderPaymentChargeId()
    {
        return $this->providerPaymentChargeId;
    }

    /**
     * <AUTHOR>
     *
     * @param mixed $providerPaymentChargeId
     *
     * @return void
     */
    public function setProviderPaymentChargeId($providerPaymentChargeId)
    {
        $this->providerPaymentChargeId = $providerPaymentChargeId;
    }

    /**
     * <AUTHOR>
     *
     * @return OrderInfo|null
     */
    public function getOrderInfo()
    {
        return $this->orderInfo;
    }

    /**
     * <AUTHOR>
     *
     * @param OrderInfo $orderInfo
     *
     * @return void
     */
    public function setOrderInfo($orderInfo)
    {
        $this->orderInfo = $orderInfo;
    }
}
